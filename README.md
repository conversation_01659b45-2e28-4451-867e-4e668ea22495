
# CareComms React Native App

基於 design.html 設計規範實現的 React Native 應用程式，使用 React Native Paper 作為主要 UI 組件庫，遵循 Material Design 設計原則。

## 功能特色

### ✅ 已實現功能

1. **主頁面設計**
   - 頂部標題欄：深藍色背景，應用 Logo 和 CareComms 標題
   - 功能卡片網格：Profile 和 Edit Group 卡片
   - Quick Actions 區域：Search User 和 View Notifications 按鈕

2. **底部導航**
   - Home 頁面（當前活動）
   - 中央浮動加號按鈕
   - Notifications 頁面

3. **技術實現**
   - React Native Paper UI 組件庫
   - Material Design 圖標
   - TypeScript 類型安全
   - 響應式設計
   - 主題配置支持

## 技術棧

- **React Native**: 0.79.2
- **React Native Paper**: 5.14.5
- **Expo Router**: 5.0.7
- **TypeScript**: 5.8.3
- **Material Design Icons**: @expo/vector-icons

## 開始使用

1. 安裝依賴

   ```bash
   pnpm install
   ```

2. 啟動應用

   ```bash
   pnpm start
   ```

3. 代碼質量檢查

   ```bash
   # TypeScript 類型檢查
   pnpm type-check

   # ESLint 代碼檢查
   pnpm lint
   ```

在輸出中，您可以選擇在以下環境中打開應用：

- [開發構建](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android 模擬器](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS 模擬器](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go)

您可以通過編輯 **app** 目錄中的文件開始開發。此項目使用[基於文件的路由](https://docs.expo.dev/router/introduction)。

## Get a fresh project

When you're ready, run:

```bash
pnpm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.



#### **母嬰轉送即時通知App**

**項目目標：** 開發一款即時通知應用程式，允許醫院護理師（發起人）向指定的其他護理師或群組（接收人）發送關於母嬰轉送的緊急通知，並追蹤接收人的確認狀態。

**一、數據流 (Dataflow)**

整體數據流將圍繞以下幾個核心組件：



1. **用戶端 App (React Native - Expo)：**
    * **發起人 (Initiator)：** 輸入轉送資訊，選擇接收人，發送通知請求。監控接收人狀態。
    * **接收人 (Recipient)：** 接收推播通知，查看通知詳情，確認收到 ("Noted")。
2. **後端服務 (BaaS - 例如 Firebase)：**
    * **Firebase Authentication (可選，初期用DeviceID)：** 用於更進階的用戶管理（如果需要）。
    * **Firestore/Realtime Database：**
        * 儲存用戶基本資訊（DeviceID, Nickname, FCM Token）。
        * **Realtime Database (推薦用於此場景的即時狀態更新)：** 儲存「通知事件」的即時狀態，包括發起人資訊、事件詳情、各接收人的確認狀態 (紅/黃/綠)。
    * **Firebase Cloud Functions (或選擇的BaaS對應的伺服器端邏輯)：**
        * 處理來自發起人的通知創建請求。
        * 根據選擇的接收人，查詢其 FCM Token。
        * 調用 FCM API 發送推播通知。
        * 處理來自接收人的 "Noted" 回應，更新 Realtime Database 中的事件狀態。
        * 處理發起人取消事件的請求。
    * **Firebase Cloud Messaging (FCM)：** 向目標設備發送推播通知。
3. **用戶端本地儲存 (例如 AsyncStorage, SQLite, WatermelonDB)：**
    * 接收人 App 將收到的通知事件詳情儲存在本地，以便在24小時內查閱和觸發重複提醒。
    * 儲存用戶的 Nickname。

**數據流示意圖：**

sequenceDiagram \
    participant I as Initiator App \
    participant BE as Backend (Cloud Functions + Realtime DB/Firestore) \
    participant FCM \
    participant R as Recipient App \
    participant LS as Recipient Local Storage \
 \
    %% Step 1: Initiator Creates Alert \
    I->>BE: 1. Create Alert Event (Details, TargetRecipients) \
    BE->>BE: 2. Store Event (initial status) in Realtime DB \
    BE->>FCM: 3. For each TargetRecipient: Send Push Notification (via FCM Token) \
    FCM-->>R: 4. Push Notification Delivered \
 \
    %% Step 2 & 3: Recipient Receives & Acknowledges \
    R->>R: 5. Receive Push, Display Alert \
    R->>LS: 6. Store Alert Details Locally (for 24hr access & recurring reminder) \
    R->>R: 7. Start Recurring Local Alert (every 1 min if not noted) \
    R->>BE: 8. User clicks "Noted" (EventID, RecipientID) \
    BE->>BE: 9. Update Recipient Status in Realtime DB (to Green) \
    R->>R: 10. Stop Recurring Local Alert, Update Local Stored Alert \
 \
    %% Step 4: Initiator Monitors \
    BE-->>I: 11. Realtime DB updates Initiator's Panel (Recipient Status Changes R/Y/G) \
 \
    %% Optional: Initiator Cancels Event \
    I->>BE: 12. Cancel Event Request \
    BE->>BE: 13. Update Event Status to "Cancelled" in Realtime DB \
    BE-->>R: 14. (Optional) Send "Cancelled" Push to un-noted Recipients \


**二、使用者介面 (UI) - 主要畫面與組件**



1. **啟動與用戶識別：**
    * 首次啟動時，提示用戶輸入一個 **Nickname**。如果未輸入，則App內部使用 DeviceID，對外顯示時也可能是 DeviceID 或提示用戶設定。Nickname 儲存在本地。
2. **主畫面 (Initiator - 發起通知 / Recipient - 查看歷史通知)：**
    * 可能有個Tab或切換來區分「發起新通知」和「我收到的通知列表」。
    * **發起新通知按鈕。**
3. **發起通知畫面 (Step 1 - Initiator)：**
    * **標題：** "發起母嬰轉送通知"
    * **個案類型 (Case Type)：** 下拉選單
        * "母親與嬰兒轉送 (Mother and Baby Transfer)"
        * "僅母親轉送 (Mother Only Transfer)"
        * "嬰兒轉送至 NICU/SCBU (Baby Transfer to NICU/SCBU)"
    * **母親姓名縮寫 (Mother's Initial)：** 文字輸入框 (例如：C.M.L.)
    * **床號 (Bed Number)：** 文字輸入框 (例如：A301)。*根據個案類型顯示/隱藏或設為可選。*
    * **目標病房/單位 (Designated Ward)：** 文字輸入框 (手動輸入，例如：5B, NICU)。*此欄位僅為事件描述的一部分，不直接用於接收人路由。*
    * **診斷或臨床記錄 (Diagnosis or Clinical Note)：** 多行文字輸入區域，較大，有 Placeholder "若無特別記錄可留空 (Leave blank if no notes)"。
    * **選擇接收人 (Select Recipients)：**
        * 一個按鈕或區域，點擊後彈出用戶選擇列表。
        * 用戶選擇列表：顯示所有已註冊用戶的 Nickname (或 DeviceID)。可多選。
        * (未來可擴展為選擇群組)。
    * **發送通知按鈕。**
4. **接收通知畫面 (Step 2 & 3 - Recipient)：**
    * **系統推播通知樣式：**
        * 標題："[緊急] 母嬰轉送通知"
        * 內容：簡要資訊，例如："個案: 母親與嬰兒轉送, 母親: C.M.L., 床號: A301, 目標: 5B。請確認。" 或 "個案: 嬰兒轉送, 母親: T.P.L., 目標: NICU, 備註: (部分備註內容)... 請確認。"
        * 高優先級，應能觸發聲音和震動。
    * **App內通知詳情畫面 (點擊推播通知後進入)：**
        * 顯示完整的通知資訊（同發起畫面輸入的內容）。
        * 發起人：[Initiator's Nickname]
        * 發起時間：[Timestamp]
        * **"我已收到 (Noted)" 按鈕：** 醒目的大按鈕。
5. **發起人監控面板 (Step 4 - Initiator)：**
    * 此面板可能在發起通知成功後自動跳轉，或從主畫面進入「我發起的通知」列表，點選特定事件查看。
    * **事件標題/摘要：** 簡要顯示此事件的關鍵資訊。
    * **接收人列表：**
        * 每一行列出一個接收人的 Nickname。
        * 狀態指示燈/圖標：
            * **紅色 (●)：** 未收到 (FCM 發送失敗或目標用戶 FCM Token 失效)。
            * **黃色 (●)：** 已發送，等待對方確認 (FCM 已成功發送，等待對方 App 回應 "Noted")。
            * **綠色 (●)：** 已確認 (對方已點擊 "Noted")。
    * **(可選/V2) 取消/結束此通知事件按鈕。**
6. **我收到的通知列表 (Recipient)：**
    * 按時間倒序列出過去24小時內收到的通知。
    * 每條通知摘要顯示關鍵資訊和是否已 "Noted"。
    * 點擊可查看完整詳情。超過24小時的通知自動清除。

**三、程式邏輯 (Program Logic)**

**A. 用戶端 (React Native - Expo App)**



1. **初始化與用戶身份：**
    * App 啟動時，獲取 DeviceID。
    * 檢查本地是否已儲存 Nickname。若無，提示用戶輸入。
    * 向後端註冊/更新用戶資訊：{ deviceID, nickname, fcmToken }。fcmToken 在 App 啟動或 Token 刷新時獲取並更新到後端。
2. **發起通知 (Initiator)：**
    * 用戶填寫表單，選擇接收人列表。
    * 點擊「發送通知」：
        * 收集所有輸入數據，形成一個 alertEvent JSON 物件。
        * 調用後端 Cloud Function (例如 createAlert)，傳遞 alertEvent。
        * 成功後，導航到監控面板，並開始監聽 Realtime Database 中該事件的狀態變化。
3. **接收通知與處理 (Recipient)：**
    * **FCM 監聽器：** App 在前景和背景都需要能接收 FCM 消息。
    * **收到推播通知時：**
        * 解析通知內容。
        * 將通知詳情儲存到本地儲存（SQLite/AsyncStorage等），標記為「未確認」，記錄接收時間戳。
        * 顯示系統通知。
        * **啟動本地重複提醒機制：** 使用 expo-notifications 的 scheduleNotificationAsync 或類似機制，設定一個每隔1分鐘觸發的本地通知（帶震動），直到此事件被標記為「已確認」或超過24小時或被發起人取消。該重複通知的內容應與初次通知類似或更簡潔。
    * **用戶點擊通知進入 App 或直接在 App 內操作：**
        * 顯示通知詳情畫面。
        * 用戶點擊「我已收到 (Noted)」按鈕：
            * 調用後端 Cloud Function (例如 acknowledgeAlert)，傳遞 eventID 和自己的 deviceID。
            * 成功後，更新本地儲存中該事件的狀態為「已確認」。
            * 取消該事件的本地重複提醒。
    * **本地儲存管理：**
        * 定期（例如 App 啟動時）檢查本地儲存的通知，刪除超過24小時的記錄，並取消其可能仍在運行的重複提醒。
4. **監控面板 (Initiator)：**
    * 訂閱 Realtime Database 中特定 eventID 的路徑。
    * 當該路徑下 recipients 狀態更新時，動態刷新接收人列表的紅/黃/綠狀態。
5. **(V2) 取消事件 (Initiator)：**
    * 用戶點擊「取消/結束此通知事件」。
    * 調用後端 Cloud Function (例如 cancelAlert)，傳遞 eventID。

**B. 後端 (Firebase Cloud Functions & Realtime Database/Firestore)**



1. **用戶註冊/更新 (例如 registerUser Function)：**
    * 接收客戶端傳來的 { deviceID, nickname, fcmToken }。
    * 在 Firestore (或您選擇的DB) 中創建或更新用戶記錄，以 deviceID 為主鍵。
2. **創建通知 (例如 createAlert Function)：**
    * 接收發起人傳來的 alertEvent JSON (包含個案詳情、發起人ID、目標接收人DeviceID列表等)。
    * 生成一個唯一的 eventID。
    * 在 Realtime Database 中創建一個新的事件記錄： \
/alertEvents/{eventID}: { \
  initiatorDeviceID: "...", \
  initiatorNickname: "...", \
  caseType: "...", \
  motherInitial: "...", \
  bedNumber: "...", \
  designatedWardInput: "...", \
  notes: "...", \
  timestampCreated: serverTimestamp(), \
  status: "active", // active, resolved, cancelled \
  recipients: { \
    // Populated in the next step \
  } \
} \

    * 對於 alertEvent 中指定的每個 targetRecipientDeviceID：
        * 從 Firestore 查詢該 targetRecipientDeviceID 的 fcmToken 和 nickname。
        * 如果找不到用戶或 fcmToken，則在 Realtime Database 的 /alertEvents/{eventID}/recipients/{targetRecipientDeviceID} 中記錄狀態為 "send_failed" (紅色)。
        * 如果找到 fcmToken：
            * 構建 FCM 消息 payload，包含必要的通知詳情 (eventID, caseType, motherInitial 等)。設定高優先級 (priority: 'high', android: { priority: 'high' }, apns: { headers: { 'apns-priority': '10' } })。
            * 調用 FCM API 發送推播通知。
            * **FCM 發送成功後**，在 Realtime Database 的 /alertEvents/{eventID}/recipients/{targetRecipientDeviceID} 中記錄： \
{ \
  nickname: "target_nickname", \
  status: "fcm_sent_pending_ack", // 黃色 (已發送，待確認) \
  fcmTokenUsed: "the_fcm_token" // 可選，用於調試 \
} \

            * **FCM 發送失敗時** (例如，FCM API 返回特定錯誤指示 Token 無效)，記錄狀態為 "send_failed" (紅色)。
3. **確認通知 (例如 acknowledgeAlert Function)：**
    * 接收接收人傳來的 { eventID, recipientDeviceID }。
    * 驗證 eventID 和 recipientDeviceID 的有效性。
    * 更新 Realtime Database 中 /alertEvents/{eventID}/recipients/{recipientDeviceID}/status 為 "acknowledged" (綠色)。
    * (可選) 記錄確認時間戳。
4. **(V2) 取消通知 (例如 cancelAlert Function)：**
    * 接收發起人傳來的 { eventID }。
    * 更新 Realtime Database 中 /alertEvents/{eventID}/status 為 "cancelled_by_initiator"。
    * (可選) 遍歷該事件下所有尚未 "acknowledged" 的接收人，給他們發送一個內容為「此通知已取消」的靜默推播或普通推播，以便客戶端可以停止本地的重複提醒。

**C. 數據結構 (後端 Realtime Database 示例)**

/users/{deviceID}: \
  nickname: "Nurse Joy" \
  fcmToken: "bk3RNwTe3H0:CI2k_HHwgIpoDKCIZhe..." \
  lastSeen: timestamp \
 \
/alertEvents/{eventID}: \
  initiatorDeviceID: "initiator_device_id" \
  initiatorNickname: "Nurse A" \
  caseType: "mother_baby_transfer" \
  motherInitial: "C.M.L." \
  bedNumber: "A301" \
  designatedWardInput: "5B" \
  notes: "BP high." \
  timestampCreated: 1678886400000 // Firebase Server Timestamp \
  status: "active" // "active", "resolved_by_initiator", "cancelled_by_initiator" \
  recipients: { \
    "recipient_device_id_1": { \
      nickname: "Nurse B", \
      status: "fcm_sent_pending_ack", // "send_failed", "fcm_sent_pending_ack", "acknowledged" \
      lastUpdateTimestamp: 1678886401000 \
    }, \
    "recipient_device_id_2": { \
      nickname: "Nurse C", \
      status: "acknowledged", \
      lastUpdateTimestamp: 1678886405000, \
      acknowledgedTimestamp: 1678886404000 // 可選 \
    }, \
    "recipient_device_id_3": { \
      nickname: "Nurse D", \
      status: "send_failed" // 例如，FCM token無效 \
    } \
  } \


**四、開發階段建議 (MVP - Minimum Viable Product)**



1. **環境搭建：**
    * 創建 Expo (React Native) 專案，使用 PNPM 管理。
    * 設置 Firebase 項目，啟用 Firestore, Realtime Database, Cloud Functions, FCM。
2. **核心功能 - 發起與接收：**
    * 實現用戶 Nickname 輸入與 DeviceID 獲取。
    * 實現用戶資訊 (DeviceID, Nickname, FCM Token) 註冊到後端。
    * 實現「發起通知畫面」UI，允許輸入所有必要資訊。
    * 實現「選擇接收人」功能 (初期可從一個固定的用戶列表或所有已註冊用戶中選擇)。
    * 後端 createAlert Function：接收請求，存儲到 Realtime DB，並向選定接收人發送 FCM。
    * 接收人 App：能接收 FCM 推播，顯示通知詳情，點擊 "Noted"。
    * 後端 acknowledgeAlert Function：更新 Realtime DB 中的狀態。
3. **核心功能 - 狀態監控與本地提醒：**
    * 發起人 App：實現監控面板，從 Realtime DB 讀取並顯示接收人狀態 (紅/黃/綠)。
    * 接收人 App：實現本地儲存通知詳情 (24小時有效)。
    * 接收人 App：實現未確認通知的每分鐘本地重複提醒 (帶震動)。
4. **測試與迭代：**
    * 在多個設備上進行完整流程測試。
    * 根據反饋進行調整。

**五、後續可考慮的擴展功能 (V2+)**



* 發起人取消/結束事件的功能。
* 更完善的錯誤處理和離線支持。
* 群組管理與按群組選擇接收人。
* 歷史通知記錄的搜索與篩選。
* 用戶狀態（在線/離線）顯示（可利用 Realtime Database 的 presence 功能）。
* 更詳細的審計日誌。
* UI/UX 的持續打磨。

這份工作計劃提供了一個初步的框架。在實際開發過程中，每個階段的細節都可以進一步細化和調整。希望這對您有所幫助！
