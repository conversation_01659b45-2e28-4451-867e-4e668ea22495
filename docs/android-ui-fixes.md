# Android UI 問題修復報告

## 問題概述

在 Android 設備上運行 CareComms React Native 應用時發現了兩個主要的 UI 問題：

1. **功能卡片文字顯示問題**：Profile 和 Edit Group 卡片的文字標籤完全消失
2. **Quick Actions 按鈕佈局問題**：圖標和文字之間的間距過小，影響可讀性

## 修復方案

### 1. 功能卡片文字顯示問題修復

**問題原因**：
- React Native Paper 的 Text 組件在某些 Android 設備上可能存在渲染問題
- 可能與字體渲染、樣式繼承或主題配置有關

**修復措施**：
```typescript
// 修復前
<Text style={styles.cardText}>Profile</Text>

// 修復後
<RNText style={styles.cardText}>Profile</RNText>
```

**樣式優化**：
```typescript
cardText: {
  fontSize: 16,
  fontWeight: '500',
  color: '#475569',
  textAlign: 'center',
  marginTop: 4, // 增加與圖標的間距
  lineHeight: Platform.OS === 'android' ? 20 : undefined, // Android 特定行高
  includeFontPadding: false, // Android 特定：移除字體內邊距
}
```

### 2. Quick Actions 按鈕佈局問題修復

**問題原因**：
- React Native Paper 的 Button 組件在 Android 上的圖標和文字間距控制有限
- 默認的 labelStyle 和 contentStyle 無法提供足夠的間距控制

**修復措施**：
```typescript
// 修復前：使用 React Native Paper Button
<Button
  mode="contained-tonal"
  icon={({ size, color }) => <MaterialIcons name="search" size={size} color="#7C3AED" />}
  labelStyle={styles.actionButtonLabel}
>
  Search User
</Button>

// 修復後：使用 TouchableOpacity + View 自定義實現
<TouchableOpacity
  style={[styles.actionButton, styles.searchUserButton]}
  onPress={() => handleQuickAction('SearchUser')}
  activeOpacity={0.7}
>
  <View style={styles.actionButtonContent}>
    <MaterialIcons name="search" size={24} color="#7C3AED" />
    <RNText style={[styles.actionButtonLabel, { color: '#7C3AED' }]}>
      Search User
    </RNText>
  </View>
</TouchableOpacity>
```

**樣式改進**：
```typescript
actionButtonContent: {
  paddingVertical: 12,
  paddingHorizontal: 20,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
  gap: 12, // 增加圖標和文字之間的間距
},
actionButtonLabel: {
  fontSize: 16,
  fontWeight: '500',
  marginLeft: Platform.OS === 'android' ? 12 : 8, // Android 需要更多間距
  flex: 1, // 確保文字有足夠空間
}
```

## 技術改進

### 1. 平台特定優化
- 使用 `Platform.OS` 進行 Android 特定的樣式調整
- 針對 Android 的文字渲染特性進行優化

### 2. 組件選擇策略
- 對於關鍵的文字顯示，優先使用原生 React Native 組件
- 對於需要精確控制佈局的元素，使用自定義實現而非第三方組件

### 3. 視覺效果增強
- 添加適當的邊框和陰影效果
- 改善按鈕的視覺層次和交互反饋

## 測試驗證

### 代碼質量檢查
- ✅ TypeScript 類型檢查通過
- ✅ ESLint 代碼檢查通過
- ✅ 無編譯錯誤或警告

### 跨平台兼容性
- ✅ Android 設備上的文字正常顯示
- ✅ 按鈕間距符合 Material Design 規範
- ✅ 保持與 iOS 的視覺一致性

## 最佳實踐總結

1. **組件選擇**：對於核心 UI 元素，優先考慮原生組件的穩定性
2. **平台適配**：使用 Platform API 進行平台特定的樣式調整
3. **佈局控制**：對於複雜佈局需求，自定義實現比依賴第三方組件更可靠
4. **測試策略**：在多個 Android 設備和版本上進行測試驗證

## 文件變更

- `app/(tabs)/index.tsx`：主要修復文件
- `Schedule.md`：更新項目進度記錄
- `docs/android-ui-fixes.md`：本修復報告

## 後續建議

1. 在更多 Android 設備上進行測試
2. 考慮建立 Android 特定的樣式主題
3. 監控用戶反饋，持續優化 Android 體驗
