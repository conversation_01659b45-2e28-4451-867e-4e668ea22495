# Android 文字可見性問題修復報告

## 問題概述

在 Android 設備上，HomePage 的 'Profile' 和 'Edit Group' 卡片文字標籤存在顯示問題，需要進行特定的優化以確保文字在 Android 平台上正確顯示。

## 修復措施

### 1. 關鍵發現：移除 cardContent 中的 flex: 1

**根本原因：**
經過實際測試發現，Android 設備上文字不顯示的主要原因是 `cardContent` 樣式中的 `flex: 1` 屬性。這個屬性在 Android 平台上會導致文字組件的佈局計算出現問題，使文字無法正確渲染。

**修復前：**
```typescript
cardContent: {
  flex: 1,  // ← 這是問題的根源
  alignItems: 'center',
  justifyContent: 'center',
  padding: 20,
},
```

**修復後：**
```typescript
cardContent: {
  alignItems: 'center',
  justifyContent: 'center',
  padding: 20,
},
```

### 2. cardText 樣式優化

**修復內容：**
- 添加 `includeFontPadding: false` - Android 特定屬性，移除字體內邊距以改善文字顯示
- 添加 `lineHeight: Platform.OS === 'android' ? 20 : undefined` - Android 特定行高優化
- 保持簡潔的樣式設計，避免複雜的視覺效果干擾文字渲染

**修復前：**
```typescript
cardText: {
  fontSize: 16,
  fontWeight: '500',
  textAlign: 'center',
  marginTop: 8,
  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
},
```

**修復後：**
```typescript
cardText: {
  fontSize: 16,
  fontWeight: '500',
  textAlign: 'center',
  marginTop: 8,
  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  lineHeight: Platform.OS === 'android' ? 20 : undefined,
  includeFontPadding: false,
},
```

### 2. Appbar 標題優化

**修復內容：**
- 使用明確的 `fontWeight: 'bold'` 內聯樣式
- 確保跨平台字體渲染一致性

**修復前：**
```typescript
<Text style={[styles.appTitle, { fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif' }]}>
  CareComms
</Text>
```

**修復後：**
```typescript
<Text style={[styles.appTitle, {
  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  fontWeight: 'bold'
}]}>
  CareComms
</Text>
```

### 3. 平台特定優化

**狀態欄高度調整：**
- Android: 從 24px 調整為 25px，提供更好的視覺平衡
- iOS: 保持 44px 不變

**圖標顏色確認：**
- Profile 卡片圖標: `#4F46E5` (indigo-600) ✓
- Edit Group 卡片圖標: `#0284C7` (sky-600) ✓
- 文字顏色: `#334155` (slate-700) ✓

## 技術細節

### Android 特定屬性說明

1. **includeFontPadding: false**
   - 移除 Android 系統默認的字體內邊距
   - 提供更精確的文字定位和顯示
   - 改善文字在容器中的對齊效果

2. **lineHeight 優化**
   - Android 平台設置明確的行高值 (20px)
   - 確保文字垂直居中和可讀性
   - iOS 平台使用系統默認值

3. **平台特定字體處理**
   - iOS: 使用 'System' 字體
   - Android: 使用 'sans-serif' 字體
   - 確保在各平台上都有良好的文字渲染效果

## 測試結果

### 代碼質量檢查
- ✅ TypeScript 類型檢查通過 (`pnpm type-check`)
- ✅ ESLint 代碼規範檢查通過 (`pnpm lint`)

### 預期改善效果
1. **文字可見性**：Android 設備上的卡片文字標籤應該清晰可見
2. **跨平台一致性**：iOS 和 Android 平台上的文字顯示效果更加一致
3. **用戶體驗**：改善整體界面的可讀性和專業外觀

## 後續建議

1. **實際設備測試**：在多種 Android 設備上測試文字顯示效果
2. **性能監控**：確認優化不會影響應用性能
3. **用戶反饋**：收集用戶對界面改善的反饋

## 相關文件

- `app/(tabs)/index.tsx` - 主要修改文件
- `Schedule.md` - 項目進度記錄
- `docs/android-ui-fixes.md` - 之前的 UI 修復記錄
