import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, View } from 'react-native';
import {
  Button,
  Card,
  Chip,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';

// 通知类型定义
export type NotificationType = 'urgent' | 'general' | 'reminder';

// 通知数据接口
export interface NotificationData {
  type: NotificationType;
  title: string;
  content: string;
  recipient: string;
  notes: string;
}

// 组件属性接口
export interface InitiateNotiProps {
  onSubmit?: (data: NotificationData) => void;
  onCancel?: () => void;
}

// 通知类型配置
const NOTIFICATION_TYPES = [
  {
    id: 'urgent' as NotificationType,
    label: '紧急通知',
    icon: 'priority-high',
    color: '#EF4444', // red-500
  },
  {
    id: 'general' as NotificationType,
    label: '一般通知',
    icon: 'info',
    color: '#3B82F6', // blue-500
  },
  {
    id: 'reminder' as NotificationType,
    label: '提醒通知',
    icon: 'schedule',
    color: '#F59E0B', // amber-500
  },
];

// 通知创建组件 - 用于构建具体的通知资料
export default function InitiateNoti({ onSubmit, onCancel }: InitiateNotiProps) {
  const theme = useTheme();
  
  // 表单状态管理
  const [selectedType, setSelectedType] = useState<NotificationType>('general');
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [recipient, setRecipient] = useState('');
  const [notes, setNotes] = useState('');

  // 处理表单提交
  const handleSubmit = () => {
    const notificationData: NotificationData = {
      type: selectedType,
      title: title.trim(),
      content: content.trim(),
      recipient: recipient.trim(),
      notes: notes.trim(),
    };

    // 基本验证
    if (!notificationData.title || !notificationData.content) {
      // TODO: 显示错误提示
      console.warn('标题和内容不能为空');
      return;
    }

    onSubmit?.(notificationData);
  };

  // 处理取消操作
  const handleCancel = () => {
    // 重置表单
    setSelectedType('general');
    setTitle('');
    setContent('');
    setRecipient('');
    setNotes('');
    onCancel?.();
  };

  // 检查表单是否有效
  const isFormValid = title.trim().length > 0 && content.trim().length > 0;

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {/* 页面标题 */}
      <View style={styles.header}>
        <Text 
          variant="headlineSmall" 
          style={[styles.headerTitle, { color: theme.colors.onBackground }]}
        >
          创建通知
        </Text>
        <Text 
          variant="bodyMedium" 
          style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}
        >
          填写通知详细信息并选择通知类型
        </Text>
      </View>

      {/* Case Type 选择器 - 小红书风格按钮组 */}
      <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text 
            variant="titleMedium" 
            style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
          >
            通知类型
          </Text>
          <View style={styles.typeButtonGroup}>
            {NOTIFICATION_TYPES.map((type) => (
              <Chip
                key={type.id}
                mode={selectedType === type.id ? 'flat' : 'outlined'}
                selected={selectedType === type.id}
                onPress={() => setSelectedType(type.id)}
                icon={({ size }) => (
                  <MaterialIcons
                    name={type.icon as any}
                    size={size}
                    color={selectedType === type.id ? theme.colors.onPrimary : type.color}
                  />
                )}
                style={[
                  styles.typeButton,
                  selectedType === type.id && {
                    backgroundColor: type.color,
                  },
                ]}
                textStyle={[
                  styles.typeButtonText,
                  {
                    color: selectedType === type.id ? theme.colors.onPrimary : theme.colors.onSurface,
                    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                  },
                ]}
              >
                {type.label}
              </Chip>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* 通知详细信息表单 */}
      <Card style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text 
            variant="titleMedium" 
            style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
          >
            通知详情
          </Text>
          
          {/* 通知标题 */}
          <TextInput
            label="通知标题 *"
            value={title}
            onChangeText={setTitle}
            mode="outlined"
            style={styles.textInput}
            maxLength={100}
            placeholder="请输入通知标题"
          />

          {/* 通知内容 */}
          <TextInput
            label="通知内容 *"
            value={content}
            onChangeText={setContent}
            mode="outlined"
            multiline
            numberOfLines={4}
            style={styles.textInput}
            maxLength={500}
            placeholder="请输入通知的详细内容"
          />

          {/* 接收者 */}
          <TextInput
            label="接收者"
            value={recipient}
            onChangeText={setRecipient}
            mode="outlined"
            style={styles.textInput}
            placeholder="请输入接收者信息（可选）"
          />

          {/* 备注 */}
          <TextInput
            label="备注"
            value={notes}
            onChangeText={setNotes}
            mode="outlined"
            multiline
            numberOfLines={2}
            style={styles.textInput}
            maxLength={200}
            placeholder="添加备注信息（可选）"
          />
        </Card.Content>
      </Card>

      {/* 操作按钮 */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={[styles.actionButton, styles.cancelButton]}
          labelStyle={[
            styles.buttonLabel,
            { fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif' }
          ]}
        >
          取消
        </Button>
        <Button
          mode="contained"
          onPress={handleSubmit}
          disabled={!isFormValid}
          style={[styles.actionButton, styles.submitButton]}
          labelStyle={[
            styles.buttonLabel,
            { fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif' }
          ]}
        >
          创建通知
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  headerTitle: {
    fontWeight: '600',
    marginBottom: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  headerSubtitle: {
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    lineHeight: Platform.OS === 'android' ? 20 : undefined,
  },
  sectionCard: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontWeight: '500',
    marginBottom: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  typeButtonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  typeButton: {
    flex: 1,
    borderRadius: 20,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: Platform.OS === 'android' ? 18 : undefined,
  },
  textInput: {
    marginBottom: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 8,
    marginBottom: 32,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
  },
  cancelButton: {
    borderWidth: 1,
  },
  submitButton: {
    // 使用主题默认样式
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '500',
    paddingVertical: 4,
    lineHeight: Platform.OS === 'android' ? 20 : undefined,
  },
});
