import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// React Native Paper 主題配置 - 匹配設計規範中的顏色
export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#4F46E5', // indigo-600
    primaryContainer: '#E0E7FF', // indigo-100
    secondary: '#0284C7', // sky-600
    secondaryContainer: '#E0F2FE', // sky-100
    tertiary: '#7C3AED', // purple-600
    tertiaryContainer: '#FAF5FF', // purple-50
    surface: '#FFFFFF',
    surfaceVariant: '#F8FAFC', // slate-50
    background: '#FFFFFF',
    onBackground: '#1F2937', // gray-800
    onSurface: '#475569', // slate-700
    onSurfaceVariant: '#64748B', // slate-500
    outline: '#E2E8F0', // slate-200
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#6366F1', // indigo-500
    primaryContainer: '#3730A3', // indigo-700
    secondary: '#0EA5E9', // sky-500
    secondaryContainer: '#0369A1', // sky-700
    tertiary: '#8B5CF6', // purple-500
    tertiaryContainer: '#6D28D9', // purple-700
    surface: '#1F2937', // gray-800
    surfaceVariant: '#374151', // gray-700
    background: '#111827', // gray-900
    onBackground: '#F9FAFB', // gray-50
    onSurface: '#E5E7EB', // gray-200
    onSurfaceVariant: '#9CA3AF', // gray-400
    outline: '#4B5563', // gray-600
  },
};
