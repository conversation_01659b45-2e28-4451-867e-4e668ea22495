<html><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
<title>Stitch Design</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style>
    .form-select {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 0.5rem center;
      background-repeat: no-repeat;
      background-size: 1.5em 1.5em;
      padding-right: 2.5rem;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
    input:not(:placeholder-shown),
    select:not(:placeholder-shown),
    textarea:not(:placeholder-shown) {
      background-color: #e6fffa;
    }
    input:focus:placeholder-shown,
    select:focus:placeholder-shown,
    textarea:focus:placeholder-shown {
      background-color: white;
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-slate-50">
<div class="relative flex size-full min-h-screen flex-col justify-between group/design-root" style='font-family: Lexend, "Noto Sans", sans-serif;'>
<div class="sticky top-0 z-10 bg-slate-50/80 backdrop-blur-md">
<div class="flex items-center p-4">
<button class="text-slate-700 hover:text-slate-900">
<span class="material-icons-outlined">close</span>
</button>
<h1 class="flex-1 text-center text-xl font-bold text-slate-900 pr-6">New Notification</h1>
</div>
<hr class="border-slate-200"/>
</div>
<div class="flex-grow overflow-y-auto px-4 pt-6 pb-20 space-y-6">
<div>
<label class="block text-sm font-medium text-slate-700 mb-1.5" for="case-type">Case Type</label>
<select class="form-select block w-full rounded-xl border-slate-300 bg-white py-3 px-4 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base placeholder-slate-400" id="case-type" name="case-type">
<option disabled="" selected="">Select Case Type</option>
<option value="mother_baby">Mother and Baby Transfer</option>
<option value="mother_only">Mother Only Transfer</option>
<option value="baby_nicu_scbu">Baby Transfer to NICU/SCBU</option>
</select>
</div>
<div>
<h3 class="text-sm font-medium text-slate-700 mb-1.5">Mother's Details</h3>
<div class="grid grid-cols-2 gap-4">
<div>
<label class="sr-only" for="mother-initial">Mother's Initial</label>
<input class="form-input block w-full rounded-xl border-slate-300 bg-white py-3 px-4 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base placeholder-slate-400" id="mother-initial" name="mother-initial" placeholder="Initial" type="text" value="AB"/>
</div>
<div>
<label class="sr-only" for="bed-number">Bed Number</label>
<input class="form-input block w-full rounded-xl border-slate-300 bg-white py-3 px-4 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base placeholder-slate-400" id="bed-number" name="bed-number" placeholder="Bed Number" type="text" value="123"/>
</div>
</div>
</div>
<div>
<label class="block text-sm font-medium text-slate-700 mb-1.5" for="ward">Designated Ward</label>
<input class="form-input block w-full rounded-xl border-slate-300 bg-white py-3 px-4 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base placeholder-slate-400" id="ward" name="ward" placeholder="Enter Ward" type="text" value="Maternity Ward A"/>
</div>
<div x-data="{ expanded: false, notes: '' }">
<button @click="expanded = !expanded" class="flex w-full items-center justify-between rounded-xl border border-slate-300 bg-white px-4 py-3 text-base font-medium text-slate-700 shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" type="button">
<div class="flex items-center gap-2">
<span :class="{ 'text-blue-500': notes }" class="material-icons-outlined text-slate-500">note_add</span>
<span>Add Clinical Notes</span>
</div>
<span class="material-icons-outlined" x-text="expanded ? 'expand_less' : 'expand_more'">expand_more</span>
</button>
<div class="mt-2" x-collapse="" x-show="expanded">
<textarea class="form-textarea block w-full rounded-xl border-slate-300 bg-white py-3 px-4 text-slate-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-base placeholder-slate-400" id="clinical-notes" name="clinical-notes" placeholder="Enter clinical notes (optional)..." rows="4" x-model="notes"></textarea>
</div>
</div>
<div>
<label class="block text-sm font-medium text-slate-700 mb-1.5" for="recipients">Recipients</label>
<div class="space-y-2 mb-3">
<div class="flex items-center space-x-2">
<span class="inline-flex items-center justify-center size-8 rounded-full bg-blue-100 text-blue-700 font-semibold text-sm">JD</span>
<span class="text-slate-700">Jane Doe (Obstetrician)</span>
</div>
<div class="flex items-center space-x-2">
<span class="inline-flex items-center justify-center size-8 rounded-full bg-purple-100 text-purple-700 font-semibold text-sm">SM</span>
<span class="text-slate-700">Sarah Miller (Midwife)</span>
</div>
<div class="flex items-center space-x-2">
<span class="inline-flex items-center justify-center size-8 rounded-full bg-green-100 text-green-700 font-semibold text-sm">PA</span>
<span class="text-slate-700">Paul Allen (Anaesthetist)</span>
</div>
</div>
<button class="flex w-full items-center justify-center gap-2 rounded-xl border border-slate-300 bg-white px-4 py-3 text-base font-medium text-slate-700 shadow-sm hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" type="button">
<span class="material-icons-outlined text-lg">group_add</span>
          Select Recipients
        </button>
</div>
</div>
<div class="fixed bottom-0 left-0 right-0 bg-slate-50/80 backdrop-blur-md p-4 border-t border-slate-200">
<button class="w-full rounded-xl bg-[#0c7ff2] py-3.5 px-5 text-base font-semibold text-white shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" type="submit">
        Send Notification
      </button>
</div>
</div>
<script>
    // Simple AlpineJS-like functionality for expand/collapse
    document.addEventListener('alpine:init', () => {
      Alpine.data('xData', () => ({
        expanded: false,
        notes: ''
      }));
      Alpine.directive('collapse', (el, {
        expression
      }, {
        evaluateLater,
        effect
      }) => {
        let show = evaluateLater(expression)
        let initial = true
        effect(() => {
          show(value => {
            if (initial) {
              el.style.height = value ? el.scrollHeight + 'px' : '0px'
              el.style.overflow = 'hidden'
              initial = false
              return
            }
            if (value) {
              el.style.height = el.scrollHeight + 'px'
            } else {
              // Set to 0 after current height for transition
              requestAnimationFrame(() => {
                el.style.height = el.scrollHeight + 'px'
                requestAnimationFrame(() => {
                  el.style.height = '0px'
                })
              })
            }
          })
        })
        el.style.transition = 'height 0.3s ease-in-out'
      })
    })
    if (typeof Alpine === 'undefined') {
      let script = document.createElement('script');
      script.src = 'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js';
      script.defer = true;
      document.head.appendChild(script);
    }
  </script>

</body></html>