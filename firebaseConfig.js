const { initializeApp } = require('firebase/app');
// const { getAnalytics } = require("firebase/analytics");
const { getAuth } = require('firebase/auth');
const { getDatabase } = require('firebase/database');
const { getFirestore } = require('firebase/firestore');


const firebaseConfig = {
  apiKey: "AIzaSyDWmByCJlz3zwo5TE8Vd2Ed4EKmWUlg0Qg",
  authDomain: "qmnoti.firebaseapp.com",
  projectId: "qmnoti",
  storageBucket: "qmnoti.appspot.com",
  messagingSenderId: "590755555605",
  appId: "1:590755555605:web:660cb1745c42231729211c",
  measurementId: "G-8F79PF308H",
  databaseURL: "https://qmnoti-default-rtdb.asia-southeast1.firebasedatabase.app/" // 修正 Realtime Database URL
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);

// 初始化服務
const firestoreDB = getFirestore(app);
const realtimeDB = getDatabase(app);
const auth = getAuth(app); // 如果使用 Auth

// 如果你需要 Analytics
// const analytics = getAnalytics(app);

module.exports = { app, auth, firestoreDB, realtimeDB };
