<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>New Notification</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<style>
    body {
      font-family: 'Roboto', sans-serif;
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
  </head>
<body class="bg-gray-800">
<div class="max-w-sm mx-auto bg-gray-100 min-h-screen">
<header class="bg-gray-800 text-white p-4 flex items-center">
<button class="material-icons">close</button>
<h1 class="text-xl font-semibold ml-4">New Notification</h1>
</header>
<main class="bg-white rounded-t-xl mt-4 shadow-lg flex flex-col h-[calc(100vh-64px-1rem)]">
<div class="p-4 border-b border-gray-200">
<div class="flex items-center mb-4">
<button class="material-icons text-gray-600">arrow_back</button>
<h2 class="text-lg font-semibold ml-2 text-gray-800">Select Recipients</h2>
</div>
<div class="relative">
<span class="material-icons absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">search</span>
<input class="w-full pl-10 pr-4 py-2 bg-gray-100 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none" placeholder="Search by name or role" type="text"/>
</div>
</div>
<div class="flex-grow p-4 space-y-6 overflow-y-auto">
<div>
<h3 class="text-sm font-semibold text-blue-600 bg-blue-100 px-3 py-1 rounded-md inline-block mb-3">GROUPS</h3>
<ul class="space-y-3">
<li class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white mr-3">
<span class="material-icons">group</span>
</div>
<span class="text-gray-700 font-medium">Management Team</span>
</div>
<span class="material-icons text-gray-400">radio_button_unchecked</span>
</li>
<li class="flex items-center justify-between p-3 bg-blue-500 rounded-lg shadow-sm text-white">
<div class="flex items-center">
<div class="w-10 h-10 bg-white rounded-full flex items-center justify-center text-blue-500 mr-3">
<span class="material-icons">group</span>
</div>
<span class="font-medium">All Employees</span>
</div>
<span class="material-icons">check_circle</span>
</li>
<li class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white mr-3">
<span class="material-icons">group</span>
</div>
<span class="text-gray-700 font-medium">Project Alpha Team</span>
</div>
<span class="material-icons text-gray-400">radio_button_unchecked</span>
</li>
</ul>
</div>
<div>
<h3 class="text-sm font-semibold text-green-600 bg-green-100 px-3 py-1 rounded-md inline-block mb-3">INDIVIDUALS</h3>
<ul class="space-y-3">
<li class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full mr-3 flex items-center justify-center bg-purple-500 text-white text-sm font-semibold">AJ</div>
<div>
<p class="text-gray-700 font-medium">Alice Johnson</p>
</div>
</div>
<span class="material-icons text-gray-400">radio_button_unchecked</span>
</li>
<li class="flex items-center justify-between p-3 bg-blue-500 rounded-lg shadow-sm text-white">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full mr-3 border-2 border-white flex items-center justify-center bg-white text-blue-500 text-sm font-semibold">RB</div>
<div>
<p class="font-medium">Robert Brown</p>
</div>
</div>
<span class="material-icons">check_circle</span>
</li>
<li class="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm">
<div class="flex items-center">
<div class="w-10 h-10 rounded-full mr-3 flex items-center justify-center bg-teal-500 text-white text-sm font-semibold">ED</div>
<div>
<p class="text-gray-700 font-medium">Emily Davis</p>
</div>
</div>
<span class="material-icons text-gray-400">radio_button_unchecked</span>
</li>
</ul>
</div>
</div>
<div class="p-4 border-t border-gray-200">
<button class="w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Done (2)
                </button>
</div>
</main>
</div>

</body></html>