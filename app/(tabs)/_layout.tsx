import { MaterialIcons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'react-native-paper';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';

// 自定義中央浮動按鈕組件
function FloatingActionButton() {
  const handlePress = () => {
    console.log('Floating action button pressed');
    // TODO: 實現添加功能
  };

  return (
    <TouchableOpacity
      style={styles.floatingButton}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <MaterialIcons name="add" size={32} color="#FFFFFF" />
    </TouchableOpacity>
  );
}

export default function TabLayout() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: {
          height: 96, // 增加高度以容納浮動按鈕
          paddingBottom: Platform.OS === 'ios' ? 34 : 16, // 考慮安全區域
          paddingTop: 16,
          backgroundColor: theme.colors.surface,
          borderTopWidth: 1,
          borderTopColor: theme.colors.outline,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -4,
          },
          shadowOpacity: 0.1,
          shadowRadius: 6,
          ...Platform.select({
            ios: {
              position: 'absolute',
            },
            default: {},
          }),
        },
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '500',
          marginTop: 6,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ focused }) => (
            <MaterialIcons
              name="home"
              size={32}
              color={focused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="add"
        options={{
          title: '',
          tabBarIcon: () => <FloatingActionButton />,
          tabBarButton: (props) => (
            <View style={styles.floatingButtonContainer}>
              <TouchableOpacity
                onPress={props.onPress}
                style={styles.floatingButtonWrapper}
                activeOpacity={0.8}
              >
                <View style={[styles.floatingButton, { backgroundColor: theme.colors.primary }]}>
                  <MaterialIcons name="add" size={32} color={theme.colors.onPrimary} />
                </View>
              </TouchableOpacity>
            </View>
          ),
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            console.log('Add button pressed');
            // TODO: 實現添加功能
          },
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Notifications',
          tabBarIcon: ({ focused }) => (
            <MaterialIcons
              name="notifications"
              size={32}
              color={focused ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          ),
        }}
      />
    </Tabs>
  );
}

// 樣式定義 - 使用主題顏色支持 Dark Mode
const styles = StyleSheet.create({
  floatingButtonContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  floatingButtonWrapper: {
    marginTop: -40, // 向上偏移以創建浮動效果
  },
  floatingButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    // backgroundColor 通過主題顏色動態設置
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});
