import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

// 添加功能頁面 - 處理中央浮動按鈕的功能
export default function AddScreen() {
  return (
    <View style={styles.container}>
      <Text variant="headlineMedium" style={styles.title}>
        Add New Item
      </Text>
      <Text variant="bodyLarge" style={styles.description}>
        This is where the add functionality will be implemented.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#FFFFFF',
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
    color: '#1F2937',
  },
  description: {
    textAlign: 'center',
    color: '#6B7280',
  },
});
