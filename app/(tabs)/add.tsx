import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Text as RNText, View } from 'react-native';
import {
  Appbar,
  Button,
  Surface,
  Text,
  TextInput,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

// 案例類型選項
const CASE_TYPES = [
  { id: 'mother_baby', label: '母嬰轉院', shortLabel: '母嬰' },
  { id: 'mother_only', label: '母親轉院', shortLabel: '母親' },
  { id: 'baby_nicu_scbu', label: '嬰兒轉NICU', shortLabel: '嬰兒' },
] as const;

// 收件人數據
const RECIPIENTS = [
  { id: '1', name: '<PERSON>', role: 'Obstetrician', initials: 'JD', color: '#3B82F6' },
  { id: '2', name: '<PERSON>', role: 'Midwife', initials: 'SM', color: '#8B5CF6' },
  { id: '3', name: '<PERSON>', role: 'Anaesthetist', initials: 'PA', color: '#10B981' },
];

// 通知創建頁面 - 讓用戶構建具體的通知資料
export default function InitiateNotificationScreen() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  // 表單狀態管理
  const [selectedCaseType, setSelectedCaseType] = useState<string>('');
  const [motherInitial, setMotherInitial] = useState('');
  const [bedNumber, setBedNumber] = useState('');
  const [ward, setWard] = useState('');
  const [clinicalNotes, setClinicalNotes] = useState('');
  const [isNotesExpanded, setIsNotesExpanded] = useState(false);

  // 處理返回操作
  const handleGoBack = () => {
    router.back();
  };

  // 處理案例類型選擇
  const handleCaseTypeSelect = (caseTypeId: string) => {
    setSelectedCaseType(caseTypeId);
  };

  // 處理發送通知
  const handleSendNotification = () => {
    console.log('Sending notification...', {
      caseType: selectedCaseType,
      motherInitial,
      bedNumber,
      ward,
      clinicalNotes,
    });
    // TODO: 實現發送邏輯
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* 頂部標題欄 */}
      <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
        <Appbar.BackAction onPress={handleGoBack} />
        <Appbar.Content
          title="新建通知"
          titleStyle={[styles.headerTitle, { color: theme.colors.onSurface }]}
        />
      </Appbar.Header>

      {/* 主要內容區域 */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* 案例類型按鈕組 - 參考小紅書風格 */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            案例類型
          </Text>
          <View style={styles.caseTypeButtonGroup}>
            {CASE_TYPES.map((caseType) => (
              <Button
                key={caseType.id}
                mode={selectedCaseType === caseType.id ? 'contained' : 'outlined'}
                onPress={() => handleCaseTypeSelect(caseType.id)}
                style={[
                  styles.caseTypeButton,
                  selectedCaseType === caseType.id && { backgroundColor: theme.colors.primary }
                ]}
                labelStyle={[
                  styles.caseTypeButtonLabel,
                  {
                    color: selectedCaseType === caseType.id
                      ? theme.colors.onPrimary
                      : theme.colors.onSurface
                  }
                ]}
                contentStyle={styles.caseTypeButtonContent}
              >
                {caseType.shortLabel}
              </Button>
            ))}
          </View>
        </View>

        {/* 母親詳細信息 */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            母親詳細信息
          </Text>
          <View style={styles.motherDetailsRow}>
            <TextInput
              label="姓名縮寫"
              value={motherInitial}
              onChangeText={setMotherInitial}
              style={[styles.halfWidthInput, { backgroundColor: theme.colors.surface }]}
              mode="outlined"
              placeholder="輸入縮寫"
            />
            <TextInput
              label="床位號"
              value={bedNumber}
              onChangeText={setBedNumber}
              style={[styles.halfWidthInput, { backgroundColor: theme.colors.surface }]}
              mode="outlined"
              placeholder="輸入床位號"
            />
          </View>
        </View>

        {/* 指定病房 */}
        <View style={styles.section}>
          <TextInput
            label="指定病房"
            value={ward}
            onChangeText={setWard}
            style={[styles.fullWidthInput, { backgroundColor: theme.colors.surface }]}
            mode="outlined"
            placeholder="輸入病房名稱"
          />
        </View>

        {/* 臨床筆記 */}
        <View style={styles.section}>
          <Button
            mode="outlined"
            onPress={() => setIsNotesExpanded(!isNotesExpanded)}
            style={[styles.expandButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.expandButtonContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons
                name={isNotesExpanded ? "expand-less" : "expand-more"}
                size={size}
                color={theme.colors.onSurface}
              />
            )}
          >
            添加臨床筆記
          </Button>
          {isNotesExpanded && (
            <TextInput
              label="臨床筆記"
              value={clinicalNotes}
              onChangeText={setClinicalNotes}
              style={[styles.notesInput, { backgroundColor: theme.colors.surface }]}
              mode="outlined"
              placeholder="輸入臨床筆記（可選）..."
              multiline
              numberOfLines={4}
            />
          )}
        </View>

        {/* 收件人 */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            收件人
          </Text>
          <View style={styles.recipientsList}>
            {RECIPIENTS.map((recipient) => (
              <View key={recipient.id} style={styles.recipientItem}>
                <View style={[styles.recipientAvatar, { backgroundColor: recipient.color }]}>
                  <RNText style={styles.recipientInitials}>{recipient.initials}</RNText>
                </View>
                <RNText style={[styles.recipientText, { color: theme.colors.onSurface }]}>
                  {recipient.name} ({recipient.role})
                </RNText>
              </View>
            ))}
          </View>
          <Button
            mode="outlined"
            onPress={() => console.log('Select recipients')}
            style={[styles.selectRecipientsButton, { borderColor: theme.colors.outline }]}
            contentStyle={styles.selectRecipientsContent}
            labelStyle={{ color: theme.colors.onSurface }}
            icon={({ size }) => (
              <MaterialIcons name="group-add" size={size} color={theme.colors.onSurface} />
            )}
          >
            選擇收件人
          </Button>
        </View>
      </ScrollView>

      {/* 底部發送按鈕 */}
      <Surface style={[styles.bottomContainer, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <Button
          mode="contained"
          onPress={handleSendNotification}
          style={[styles.sendButton, { backgroundColor: theme.colors.primary }]}
          contentStyle={styles.sendButtonContent}
          labelStyle={[styles.sendButtonLabel, { color: theme.colors.onPrimary }]}
          disabled={!selectedCaseType || !motherInitial || !bedNumber || !ward}
        >
          發送通知
        </Button>
      </Surface>
    </SafeAreaView>
  );
}

// 樣式定義 - 使用主題顏色支持 Dark Mode 和平台特定字體
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100, // 為底部按鈕留出空間
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
  },
  // 案例類型按鈕組樣式 - 參考小紅書風格
  caseTypeButtonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  caseTypeButton: {
    flex: 1,
    borderRadius: 20,
    minHeight: 44,
  },
  caseTypeButtonContent: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  caseTypeButtonLabel: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 18,
      includeFontPadding: false,
    }),
  },
  // 母親詳細信息樣式
  motherDetailsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidthInput: {
    flex: 1,
  },
  fullWidthInput: {
    width: '100%',
  },
  // 臨床筆記樣式
  expandButton: {
    borderRadius: 12,
    marginBottom: 8,
  },
  expandButtonContent: {
    paddingVertical: 8,
    justifyContent: 'space-between',
    flexDirection: 'row-reverse',
  },
  notesInput: {
    marginTop: 8,
  },
  // 收件人樣式
  recipientsList: {
    marginBottom: 16,
  },
  recipientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recipientAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recipientInitials: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 16,
      includeFontPadding: false,
    }),
  },
  recipientText: {
    fontSize: 16,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
  selectRecipientsButton: {
    borderRadius: 12,
  },
  selectRecipientsContent: {
    paddingVertical: 8,
    justifyContent: 'center',
  },
  // 底部按鈕樣式
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 34 : 16, // 考慮安全區域
  },
  sendButton: {
    borderRadius: 12,
    minHeight: 48,
  },
  sendButtonContent: {
    paddingVertical: 12,
  },
  sendButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
});
