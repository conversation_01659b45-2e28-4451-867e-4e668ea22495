import React from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import InitiateNoti, { type NotificationData } from '@/components/initiate_noti';

// 添加功能頁面 - 處理中央浮動按鈕的功能，集成通知创建组件
export default function AddScreen() {
  const theme = useTheme();

  // 处理通知提交
  const handleNotificationSubmit = (data: NotificationData) => {
    console.log('通知数据提交:', data);
    // TODO: 实现通知创建逻辑
    // 可以调用API、保存到数据库等
  };

  // 处理取消操作
  const handleCancel = () => {
    console.log('取消创建通知');
    // TODO: 实现取消逻辑，可能需要导航回上一页
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <InitiateNoti
        onSubmit={handleNotificationSubmit}
        onCancel={handleCancel}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
